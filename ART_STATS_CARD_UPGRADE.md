# ArtStatsCard 组件升级文档

## 🚀 升级概述

基于现代化设计理念和用户体验优化，对 `ArtStatsCard` 组件进行了全面升级，新增了多项功能和视觉效果。

## ✨ 新增功能

### 1. 卡片变体 (Variants)
```vue
<ArtStatsCard variant="primary" />   <!-- 主题色变体 -->
<ArtStatsCard variant="success" />   <!-- 成功色变体 -->
<ArtStatsCard variant="warning" />   <!-- 警告色变体 -->
<ArtStatsCard variant="error" />     <!-- 错误色变体 -->
<ArtStatsCard variant="gradient" />  <!-- 渐变色变体 -->
```

### 2. 图标样式选项
```vue
<ArtStatsCard icon-style="circle" />   <!-- 圆形图标 -->
<ArtStatsCard icon-style="square" />   <!-- 方形图标 -->
<ArtStatsCard icon-style="rounded" />  <!-- 圆角图标 -->
<ArtStatsCard icon-style="minimal" />  <!-- 简约图标 -->
```

### 3. 趋势指示器
```vue
<ArtStatsCard :trend="8.5" />   <!-- 上升趋势 +8.5% -->
<ArtStatsCard :trend="-2.3" />  <!-- 下降趋势 -2.3% -->
```

### 4. 进度条显示
```vue
<ArtStatsCard :progress="75" />  <!-- 75% 进度 -->
<ArtStatsCard :progress="60" progress-color="#e7a03c" />
```

### 5. 交互功能
```vue
<ArtStatsCard 
  clickable 
  @click="handleClick" 
/>  <!-- 可点击卡片 -->
```

### 6. 装饰元素
```vue
<ArtStatsCard show-decoration />  <!-- 显示背景装饰 -->
```

### 7. 数值格式化
```vue
<ArtStatsCard 
  :count="1234567"
  separator=","
  prefix="$"
  suffix=""
  unit="次"
  :decimals="2"
/>
```

## 🎨 视觉效果升级

### 现代化设计
- **渐变背景**: 支持多种颜色变体的渐变背景
- **阴影效果**: 悬停时的动态阴影变化
- **圆角优化**: 更大的圆角半径，更现代的外观
- **装饰元素**: 可选的背景装饰圆点

### 动画效果
- **悬停动画**: 卡片上移和阴影变化
- **图标缩放**: 悬停时图标轻微放大
- **箭头移动**: 悬停时箭头向右移动
- **进度条动画**: 带有光泽效果的进度条

### 响应式设计
- **移动端适配**: 在小屏幕上自动调整尺寸
- **弹性布局**: 使用 Flexbox 确保内容对齐
- **文字缩放**: 根据屏幕尺寸调整字体大小

## 📝 使用示例

### 基础用法
```vue
<ArtStatsCard
  icon="&#xe651;"
  title="任务总数"
  :count="156"
  description="正在运行的调度任务"
/>
```

### 完整功能展示
```vue
<ArtStatsCard
  icon="&#xe651;"
  title="任务总数"
  :count="156"
  description="正在运行的调度任务"
  variant="primary"
  icon-style="rounded"
  icon-color="#ffffff"
  icon-bg-color="#56bdea"
  :icon-size="32"
  :trend="8.5"
  :progress="75"
  unit="个"
  clickable
  show-decoration
  @click="handleCardClick"
/>
```

### 在 XXL-Job 页面中的应用
```vue
<!-- 任务统计卡片 -->
<ArtStatsCard
  icon="&#xe651;"
  :title="$t('number.tasks')"
  :count="dataInfo.jobInfoCount"
  :description="$t('tasks.running.center.scheduling')"
  variant="primary"
  icon-style="rounded"
  :trend="8.5"
  :progress="75"
  clickable
  show-decoration
  @click="handleCardClick('tasks')"
/>

<!-- 调度统计卡片 -->
<ArtStatsCard
  icon="&#xe751;"
  :title="$t('times.scheduling')"
  :count="dataInfo.jobLogCount"
  :description="$t('scheduling.triggers.center')"
  variant="warning"
  icon-style="circle"
  :trend="-2.3"
  :progress="60"
  unit="次"
  clickable
  show-decoration
  @click="handleCardClick('scheduling')"
/>

<!-- 执行器统计卡片 -->
<ArtStatsCard
  icon="&#xe810;"
  :title="$t('executor.quantity')"
  :count="dataInfo.executorCount"
  :description="$t('machines.executor.online')"
  variant="success"
  icon-style="rounded"
  :trend="12.8"
  :progress="90"
  unit="台"
  clickable
  show-decoration
  @click="handleCardClick('executor')"
/>
```

## 🔧 API 参考

### Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `icon` | `string` | - | 图标 Unicode 编码 |
| `title` | `string` | - | 卡片标题 |
| `count` | `number` | - | 显示的数值 |
| `description` | `string` | - | 描述文本 |
| `variant` | `'default' \| 'primary' \| 'success' \| 'warning' \| 'error' \| 'gradient'` | `'default'` | 卡片变体 |
| `iconStyle` | `'circle' \| 'square' \| 'rounded' \| 'minimal'` | `'circle'` | 图标样式 |
| `iconColor` | `string` | - | 图标颜色 |
| `iconBgColor` | `string` | - | 图标背景颜色 |
| `iconSize` | `number` | `32` | 图标大小 |
| `backgroundColor` | `string` | - | 卡片背景颜色 |
| `showArrow` | `boolean` | `false` | 是否显示箭头 |
| `clickable` | `boolean` | `false` | 是否可点击 |
| `showDecoration` | `boolean` | `true` | 是否显示装饰元素 |
| `animationDuration` | `number` | `2000` | 数字动画持续时间 |
| `separator` | `string` | `','` | 数字分隔符 |
| `prefix` | `string` | - | 数字前缀 |
| `suffix` | `string` | - | 数字后缀 |
| `decimals` | `number` | `0` | 小数位数 |
| `unit` | `string` | - | 单位 |
| `trend` | `number` | - | 趋势百分比 |
| `progress` | `number` | - | 进度百分比 |
| `progressColor` | `string` | - | 进度条颜色 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `click` | `(event: MouseEvent)` | 卡片点击事件 |

### Slots

| 插槽名 | 说明 |
|--------|------|
| `action` | 自定义操作区域内容 |

## 🎯 设计理念

1. **现代化**: 使用渐变、阴影、动画等现代设计元素
2. **一致性**: 与 Art Design Pro 设计系统保持一致
3. **可访问性**: 支持键盘导航和屏幕阅读器
4. **响应式**: 完美适配各种屏幕尺寸
5. **可扩展**: 提供丰富的配置选项和插槽

## 📈 性能优化

- 使用 CSS 变量实现主题切换
- 利用 GPU 加速的 transform 属性
- 优化动画性能，避免重排重绘
- 懒加载和按需渲染

---

**升级完成时间**: 2025-01-31  
**升级工具**: Augment Agent  
**设计系统**: Art Design Pro
