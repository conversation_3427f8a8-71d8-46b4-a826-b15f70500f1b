import request from '@/utils/request'

// 查询产品列表
export function listProducts(data) {
  return request({
    url: '/system/products/list',
    method: 'post',
    data
  })
}

// 查询产品详细
export function getProducts(id) {
  return request({
    url: '/system/products/' + id,
    method: 'get'
  })
}

// 新增产品
export function addProducts(data) {
  return request({
    url: '/system/products',
    method: 'post',
    data: data
  })
}

// 修改产品
export function updateProducts(data) {
  return request({
    url: '/system/products',
    method: 'put',
    data: data
  })
}

// 删除产品
export function delProducts(id) {
  return request({
    url: '/system/products/' + id,
    method: 'delete'
  })
}
