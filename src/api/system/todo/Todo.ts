import request from '@/utils/request'

// 查询待办事项列表
export function listTodo(data) {
  return request({
    url: '/system/todo/list',
    method: 'post',
    data
  })
}

// 查询待办事项详细
export function getTodo(id) {
  return request({
    url: '/system/todo/' + id,
    method: 'get'
  })
}

// 新增待办事项
export function addTodo(data) {
  return request({
    url: '/system/todo',
    method: 'post',
    data: data
  })
}

// 修改待办事项
export function updateTodo(data) {
  return request({
    url: '/system/todo',
    method: 'put',
    data: data
  })
}

// 删除待办事项
export function delTodo(id) {
  return request({
    url: '/system/todo/' + id,
    method: 'delete'
  })
}
