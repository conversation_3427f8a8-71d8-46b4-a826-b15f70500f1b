import request from '@/utils/request'

// 查询礼簿列表
export function listGiftBooks(data) {
  return request({
    url: '/system/giftBooks/list',
    method: 'post',
    data
  })
}

// 查询礼簿详细
export function getGiftBooks(id) {
  return request({
    url: '/system/giftBooks/' + id,
    method: 'get'
  })
}

// 新增礼簿
export function addGiftBooks(data) {
  return request({
    url: '/system/giftBooks',
    method: 'post',
    data: data
  })
}

// 修改礼簿
export function updateGiftBooks(data) {
  return request({
    url: '/system/giftBooks',
    method: 'put',
    data: data
  })
}

// 删除礼簿
export function delGiftBooks(id) {
  return request({
    url: '/system/giftBooks/' + id,
    method: 'delete'
  })
}
