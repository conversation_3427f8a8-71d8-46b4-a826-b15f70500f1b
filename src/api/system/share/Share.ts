import request from '@/utils/request'

// 查询礼簿分享主列表
export function listShare(data) {
  return request({
    url: '/system/share/list',
    method: 'post',
    data
  })
}

// 查询礼簿分享主详细
export function getShare(id) {
  return request({
    url: '/system/share/' + id,
    method: 'get'
  })
}

// 新增礼簿分享主
export function addShare(data) {
  return request({
    url: '/system/share',
    method: 'post',
    data: data
  })
}

// 修改礼簿分享主
export function updateShare(data) {
  return request({
    url: '/system/share',
    method: 'put',
    data: data
  })
}

// 删除礼簿分享主
export function delShare(id) {
  return request({
    url: '/system/share/' + id,
    method: 'delete'
  })
}
