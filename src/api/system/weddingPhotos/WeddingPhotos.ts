import request from '@/utils/request'

// 查询婚纱照列表
export function listWeddingPhotos(data) {
  return request({
    url: '/system/weddingPhotos/list',
    method: 'post',
    data
  })
}

// 查询婚纱照详细
export function getWeddingPhotos(id) {
  return request({
    url: '/system/weddingPhotos/' + id,
    method: 'get'
  })
}

// 新增婚纱照
export function addWeddingPhotos(data) {
  return request({
    url: '/system/weddingPhotos',
    method: 'post',
    data: data
  })
}

export function addBatch(data) {
  return request({
    url: '/system/weddingPhotos/addBatch',
    method: 'post',
    data: data
  })
}

// 修改婚纱照
export function updateWeddingPhotos(data) {
  return request({
    url: '/system/weddingPhotos',
    method: 'put',
    data: data
  })
}

// 删除婚纱照
export function delWeddingPhotos(id) {
  return request({
    url: '/system/weddingPhotos/' + id,
    method: 'delete'
  })
}
