import request from '@/utils/request'

// 查询activiti_model列表
export function listActivitiModel(data) {
  return request({
    url: '/system/activitiModel/list',
    method: 'post',
    data
  })
}

// 查询activiti_model详细
export function getActivitiModel(id) {
  return request({
    url: '/system/activitiModel/' + id,
    method: 'get'
  })
}

// 新增activiti_model
export function addActivitiModel(data) {
  return request({
    url: '/system/activitiModel',
    method: 'post',
    data: data
  })
}

// 修改activiti_model
export function updateActivitiModel(data) {
  return request({
    url: '/system/activitiModel',
    method: 'put',
    data: data
  })
}

// 删除activiti_model
export function delActivitiModel(id) {
  return request({
    url: '/system/activitiModel/' + id,
    method: 'delete'
  })
}

// 流程部署
export function deploy(params) {
  return request({
    url: '/deploy',
    method: 'get',
    params
  })
}

// 下载xml
export function donwloadXml(params) {
  return request({
    url: '/model/donwloadXml',
    method: 'get',
    responseType: 'blob',
    params
  })
}

// 获取流程信息
export function getModelInfo(params) {
  return request({
    url: '/model/modelInfo',
    method: 'get',
    params
  })
}

// 发起一个测试流程
export function start(params) {
  return request({
    url: '/model/start',
    method: 'get',
    params
  })
}

/**
 * 初始化流程
 * @param data
 */
export function initModelLang(data) {
  return request({
    url: '/model/initModelLang/' + data,
    method: 'get'
  })
}
