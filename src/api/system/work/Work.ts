import request from '@/utils/request'

// 查询待办列表
export function listWork(data) {
  return request({
    url: '/system/work/list',
    method: 'post',
    data
  })
}

// 查询待办详细
export function getWork(id) {
  return request({
    url: '/system/work/' + id,
    method: 'get'
  })
}

// 新增待办
export function addWork(data) {
  return request({
    url: '/system/work',
    method: 'post',
    data: data
  })
}

// 修改待办
export function updateWork(data) {
  return request({
    url: '/system/work',
    method: 'put',
    data: data
  })
}

// 删除待办
export function delWork(id) {
  return request({
    url: '/system/work/' + id,
    method: 'delete'
  })
}
