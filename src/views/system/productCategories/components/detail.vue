<template>
  <Drawer v-model="open" @open="init">
    <template #title>
      <p>{{ title }}</p>
    </template>
    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-row>
          <!--分类名称-->
          <el-col :span="12">
            <el-form-item :label="$t('productCategories.name')" prop="name">
              {{ form.name }}
            </el-form-item>
          </el-col>
          <!--分类图标-->
          <el-col :span="12">
            <el-form-item :label="$t('productCategories.icon')">
              <el-image
                :initial-index="4"
                :max-scale="7"
                :min-scale="0.2"
                :preview-src-list="[form.icon]"
                :src="form.icon"
                :zoom-rate="1.2"
                fit="cover"
                style="width: 100px; height: 100px"
              />
            </el-form-item>
          </el-col>
          <!--排序-->
          <el-col :span="12">
            <el-form-item :label="$t('productCategories.sortOrder')" prop="sortOrder">
              {{ form.sortOrder }}
            </el-form-item>
          </el-col>
          <!--状态:0=禁用,1=启用-->
          <el-col :span="12">
            <el-form-item :label="$t('productCategories.status')" prop="status">
              <!--<dict-tag :options="" :value="form.status"/>-->
            </el-form-item>
          </el-col>
        </el-row>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  const { proxy } = getCurrentInstance()
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'

  import { computed, toRefs } from 'vue'
  import { getProductCategories } from '@/api/system/productCategories/ProductCategories'

  const emits = defineEmits(['update:show'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: String,
      default: ''
    }
  })
  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })
  const data = reactive({
    form: {}
  })

  const { form } = toRefs(data)

  const init = () => {
    if (props.id) {
      getProductCategories(props.id).then((response) => {
        data.form = response.data
      })
    }
  }
</script>
