<template>
  <div>
    <DrawerTitle>
      <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
    </DrawerTitle>
    <h1>This is Bigtian</h1>
  </div>
</template>

<script lang="ts" setup>
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'

  const submitForm = () => {
    console.log('Bigtian form submitted')
  }
  defineExpose({ submitForm })
</script>

<style lang="scss" scoped></style>
