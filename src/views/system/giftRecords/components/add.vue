<template>
  <Drawer v-model="open" @open="init">
    <template #title>
      <p>{{ title }}</p>
    </template>
    <template #btn_group>
      <el-row>
        <el-button :loading="loadingBtn.submit" type="primary" @click="submitForm"
          >{{ $t('intl.btn.submit') }}
        </el-button>
      </el-row>
    </template>

    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-form ref="giftRecordsRef" :model="form" :rules="rules">
          <el-row>
            <!--所属礼簿ID-->
            <el-col :span="12">
              <el-form-item :label="$t('giftRecords.bookId')" prop="bookId">
                <el-input
                  v-model="form.bookId"
                  :placeholder="formatStr($t('input.please'), $t('giftRecords.bookId'))"
                />
              </el-form-item>
            </el-col>

            <!--送礼人姓名-->
            <el-col :span="12">
              <el-form-item :label="$t('giftRecords.name')" prop="name">
                <el-input
                  v-model="form.name"
                  :placeholder="formatStr($t('input.please'), $t('giftRecords.name'))"
                />
              </el-form-item>
            </el-col>

            <!--礼金金额-->
            <el-col :span="12">
              <el-form-item :label="$t('giftRecords.amount')" prop="amount">
                <el-input
                  v-model="form.amount"
                  :placeholder="formatStr($t('input.please'), $t('giftRecords.amount'))"
                />
              </el-form-item>
            </el-col>

            <!--礼金类型(字典：gift_money_type)-->
            <el-col :span="12"> </el-col>

            <!--收礼日期-->
            <el-col :span="12">
              <el-form-item :label="$t('giftRecords.date')" prop="date">
                <el-date-picker
                  clearable
                  style="width: 100%"
                  v-model="form.date"
                  type="date"
                  value-format="YYYY-MM-DD"
                  :placeholder="formatStr($t('select.please'), $t('giftRecords.date'))"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  const { proxy } = getCurrentInstance()
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import { computed, toRefs } from 'vue'
  import {
    addGiftRecords,
    getGiftRecords,
    updateGiftRecords
  } from '@/api/system/tGiftRecords/TGiftRecords'

  const emits = defineEmits(['update:show', 'refreshList'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: String,
      default: ''
    }
  })
  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })
  const data = reactive({
    form: {},
    rules: {
      bookId: [
        {
          required: true,
          message: proxy.formatStr(proxy.$t('giftRecords.bookId'), proxy.$t('not.empty')),
          trigger: ['blur', 'change']
        }
      ],
      name: [
        {
          required: true,
          message: proxy.formatStr(proxy.$t('giftRecords.name'), proxy.$t('not.empty')),
          trigger: ['blur', 'change']
        }
      ],
      amount: [
        {
          required: true,
          message: proxy.formatStr(proxy.$t('giftRecords.amount'), proxy.$t('not.empty')),
          trigger: ['blur', 'change']
        }
      ],
      type: [
        {
          required: true,
          message: proxy.formatStr(proxy.$t('giftRecords.type'), proxy.$t('not.empty')),
          trigger: 'change'
        }
      ],
      date: [
        {
          required: true,
          message: proxy.formatStr(proxy.$t('giftRecords.date'), proxy.$t('not.empty')),
          trigger: ['blur', 'change']
        }
      ]
    }
  })

  const { form, rules } = toRefs(data)
  const loadingBtn = reactive({
    submit: false
  })

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['giftRecordsRef'].validate((valid) => {
      if (valid) {
        loadingBtn.submit = true

        if (form.value.id != null) {
          update()
        } else {
          add()
        }
      }
    })
  }

  const update = () => {
    updateGiftRecords(form.value)
      .then(() => {
        proxy.$modal.msgSuccess(proxy.$t('intl.message.updateSuccess'))
        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }
  const add = () => {
    addGiftRecords(form.value)
      .then(() => {
        proxy.$modal.msgSuccess(proxy.$t('common.message.addSuccess'))

        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }

  const init = () => {
    reset()
    if (props.id) {
      getGiftRecords(props.id).then((response) => {
        data.form = response.data
      })
    }
  }

  // 表单重置
  function reset() {
    form.value = {
      id: null,
      bookId: null,
      name: null,
      amount: null,
      type: null,
      date: null
    }
    proxy.resetForm('giftRecordsRef')
  }
</script>
