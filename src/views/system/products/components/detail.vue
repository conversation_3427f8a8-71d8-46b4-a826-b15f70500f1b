<template>
  <Drawer v-model="open" @open="init">
    <template #title>
      <p>{{ title }}</p>
    </template>
    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('products.name')" prop="name">
              {{ form.name }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('products.subtitle')" prop="subtitle">
              {{ form.subtitle }}
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item :label="$t('products.coverImage')">
              <el-image
                :initial-index="4"
                :max-scale="7"
                :min-scale="0.2"
                :preview-src-list="[form.coverImage]"
                :src="form.coverImage"
                :zoom-rate="1.2"
                fit="cover"
                style="width: 100px; height: 100px"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('products.detailImages')">
              <div class="big-flex big-gap-1">
                <el-image
                  v-for="(item, index) in form.detailImages.split(',')"
                  :initial-index="4"
                  :max-scale="7"
                  :min-scale="0.2"
                  :preview-src-list="[item]"
                  :src="item"
                  :zoom-rate="1.2"
                  fit="cover"
                  style="width: 100px; height: 100px"
                />
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('products.originalPrice')" prop="originalPrice">
              {{ form.originalPrice }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('products.sellingPrice')" prop="sellingPrice">
              {{ form.sellingPrice }}
            </el-form-item>
          </el-col>
          <el-col v-if="form.status === 'pre_sales'" :span="12">
            <el-form-item :label="$t('products.presalePrice')" prop="presalePrice">
              {{ form.presalePrice }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('products.totalStock')" prop="totalStock">
              {{ form.totalStock }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('products.status')" prop="status">
              <dict-tag :options="product_status" :value="form.status" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('products.isRecommend')" prop="isRecommend">
              <dict-tag :options="sys_yes_no" :value="form.isRecommend" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('products.isNew')" prop="isNew">
              <dict-tag :options="sys_yes_no" :value="form.isNew" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('products.isHot')" prop="isHot">
              <dict-tag :options="sys_yes_no" :value="form.isHot" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('products.description')" prop="description">
              {{ form.description }}
            </el-form-item>
          </el-col>
        </el-row>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  const { proxy } = getCurrentInstance()
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'

  import { computed, toRefs } from 'vue'
  import { getProducts } from '@/api/system/products/Products'

  const { product_status, sys_yes_no } = proxy.useDict('product_status', 'sys_yes_no')
  const emits = defineEmits(['update:show'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: String,
      default: ''
    }
  })
  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })
  const data = reactive({
    form: {
      detailImages: ''
    }
  })

  const { form } = toRefs(data)

  const init = () => {
    if (props.id) {
      getProducts(props.id).then((response) => {
        data.form = response.data
      })
    }
  }
</script>
