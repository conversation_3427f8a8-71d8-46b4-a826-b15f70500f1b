<template>
  <Drawer v-model="open" @open="init">
    <template #title>
      <p>{{ title }}</p>
    </template>
    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-row>
          <!--openid-->
          <el-col :span="12">
            <el-form-item :label="$t('wechatUser.openId')" prop="openId">
              {{ form.openId }}
            </el-form-item>
          </el-col>
          <!--昵称-->
          <el-col :span="12">
            <el-form-item :label="$t('wechatUser.nickname')" prop="nickname">
              {{ form.nickname }}
            </el-form-item>
          </el-col>
          <!--头像-->
          <el-col :span="12">
            <el-form-item :label="$t('wechatUser.avatar')" prop="avatar">
              {{ form.avatar }}
            </el-form-item>
          </el-col>
        </el-row>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  const { proxy } = getCurrentInstance()
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'

  import { computed, toRefs } from 'vue'
  import { getWechatUser } from '@/api/system/wechatUser/WechatUser'

  const emits = defineEmits(['update:show'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: String,
      default: ''
    }
  })
  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })
  const data = reactive({
    form: {}
  })

  const { form } = toRefs(data)

  const init = () => {
    data.form = {}
    if (props.id) {
      getWechatUser(props.id).then((response) => {
        data.form = response.data
      })
    }
  }
</script>
