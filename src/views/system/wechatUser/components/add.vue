<template>
  <Drawer v-model="open" @open="init">
    <template #title>
      <p>{{ title }}</p>
    </template>
    <template #btn_group>
      <el-row>
        <el-button :loading="loadingBtn.submit" type="primary" @click="submitForm"
          >{{ $t('intl.btn.submit') }}
        </el-button>
      </el-row>
    </template>

    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-form ref="wechatUserRef" :model="form" :rules="rules">
          <el-row>
            <!--openid-->
            <el-col :span="12">
              <el-form-item :label="$t('wechatUser.openId')" prop="openId">
                <el-input
                  v-model="form.openId"
                  :placeholder="formatStr($t('input.please'), $t('wechatUser.openId'))"
                />
              </el-form-item>
            </el-col>

            <!--昵称-->
            <el-col :span="12">
              <el-form-item :label="$t('wechatUser.nickname')" prop="nickname">
                <el-input
                  v-model="form.nickname"
                  :placeholder="formatStr($t('input.please'), $t('wechatUser.nickname'))"
                />
              </el-form-item>
            </el-col>

            <!--头像-->
            <el-col :span="12">
              <el-form-item :label="$t('wechatUser.avatar')" prop="avatar">
                <el-input
                  v-model="form.avatar"
                  :placeholder="formatStr($t('input.please'), $t('wechatUser.avatar'))"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  const { proxy } = getCurrentInstance()
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import { listUser } from '@/api/system/user'
  import { listDept } from '@/api/system/dept'
  import { computed, toRefs } from 'vue'
  import {
    addWechatUser,
    getWechatUser,
    updateWechatUser
  } from '@/api/system/wechatUser/WechatUser'

  const emits = defineEmits(['update:show', 'refreshList'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: String,
      default: ''
    }
  })
  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })
  const data = reactive({
    form: {},
    rules: {
      nickname: [
        {
          required: true,
          message: proxy.formatStr(proxy.$t('wechatUser.nickname'), proxy.$t('not.empty')),
          trigger: ['blur', 'change']
        }
      ]
    }
  })

  const { form, rules } = toRefs(data)
  const loadingBtn = reactive({
    submit: false
  })

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['wechatUserRef'].validate((valid) => {
      if (valid) {
        loadingBtn.submit = true

        if (form.value.id != null) {
          update()
        } else {
          add()
        }
      }
    })
  }

  const update = () => {
    updateWechatUser(form.value)
      .then((response) => {
        proxy.$modal.msgSuccess(proxy.$t('intl.message.updateSuccess'))
        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }
  const add = () => {
    addWechatUser(form.value)
      .then((response) => {
        proxy.$modal.msgSuccess(proxy.$t('common.message.addSuccess'))

        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }

  const init = () => {
    reset()
    if (props.id) {
      getWechatUser(props.id).then((response) => {
        data.form = response.data
      })
    }
  }

  // 表单重置
  function reset() {
    form.value = {
      id: null,
      openId: null,
      nickname: null,
      avatar: null
    }
    proxy.resetForm('wechatUserRef')
  }

  const handleUserSelect = (item) => {
    if (item === '') {
      data.userList = []
      return
    }
    listUser({
      nickName: item
    }).then((res) => {
      data.userList = res.rows
    })
  }

  const handleDeptSelect = (item) => {
    if (item === '') {
      data.deptList = []
      return
    }
    listDept({
      deptName: item
    }).then((res) => {
      data.deptList = res.data
    })
  }
</script>
