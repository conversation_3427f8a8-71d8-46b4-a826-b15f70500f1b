<template>
  <div class="page-content">
    <high-query
      @refresh="refresh"
      @search="search"
      @load="search"
      :formKey="data.fromKey"
      :formName="data.fromName"
    />

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:wechatUser:add']"
          >{{ $t('button.add') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:wechatUser:edit']"
          >{{ $t('action.modify') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:wechatUser:remove']"
          >{{ $t('button.delete') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:wechatUser:export']"
          >{{ $t('button.export') }}
        </el-button>
      </el-col>
    </el-row>

    <art-table
      v-loading="loading"
      :data="wechatUserList"
      :page-num="queryParams.pageNum"
      @selection-change="handleSelectionChange"
      :page-size="queryParams.pageSize"
      :total="total"
      @search="search"
    >
      <el-table-column type="selection" width="55" />
      <!--${comment}-->
      <!--openid-->
      <el-table-column :label="$t('wechatUser.openId')" prop="openId" :show-overflow-tooltip="true">
        <template #default="{ row }">
          <el-link type="primary" @click="visibleDetail(row)">{{ row.openId }}</el-link>
        </template>
      </el-table-column>
      <!--昵称-->
      <el-table-column
        :label="$t('wechatUser.nickname')"
        prop="nickname"
        :show-overflow-tooltip="true"
      />
      <!--头像-->
      <el-table-column :label="$t('wechatUser.avatar')" prop="avatar" :show-overflow-tooltip="true">
        <template #default="{ row }">
          <el-image
            style="width: 50px; height: 50px"
            class="big-rounded-lg"
            :src="row.avatar"
            preview-teleported
            :preview-src-list="[row.avatar]"
            lazy
            fit="cover"
            alt="avatar"
          />
        </template>
      </el-table-column>
      <el-table-column :label="$t('table.operation')" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            link
            icon="Edit"
            type="primary"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:wechatUser:edit']"
            >{{ $t('action.modify') }}
          </el-button>
          <el-button
            link
            icon="Delete"
            type="danger"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:wechatUser:remove']"
            >{{ $t('button.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </art-table>

    <!-- 添加或修改微信小程序对话框 -->

    <add
      v-model:show="open"
      :title="title"
      :id="id"
      @refreshList="refreshList"
      @close="handleClose"
    />
    <!-- 详情微信小程序对话框 -->
    <detail v-model:show="detailShow" :title="title" :id="id" @close="handleDetailClose" />
  </div>
</template>

<script setup name="WechatUser">
  import { listWechatUser, delWechatUser } from '@/api/system/wechatUser/WechatUser'
  import Add from './components/add.vue'
  import Detail from './components/detail.vue'
  import HighQuery from '@/components/HighQuery/HighQuery.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import { listUser } from '@/api/system/user'
  import { listDept } from '@/api/system/dept'

  const { proxy } = getCurrentInstance()

  const wechatUserList = ref([])
  const open = ref(false)
  const detailShow = ref(false)
  const loading = ref(true)
  const showSearch = ref(true)
  const ids = ref([])
  const id = ref()
  const names = ref([])
  const single = ref(true)
  const multiple = ref(true)
  const total = ref(0)
  const title = ref('')

  const data = reactive({
    fromKey: 't_wechat_user',
    fromName: '微信小程序',
    deptList: [],
    userList: [],

    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 20
    }
  })

  const { queryParams, form, rules } = toRefs(data)

  function refresh(query) {
    query.pageNum = 1
    search(query)
  }

  const handleUserSelect = (item) => {
    if (item === '') {
      data.userList = []
      return
    }
    listUser({
      nickName: item
    }).then((res) => {
      data.userList = res.rows
    })
  }

  const handleDeptSelect = (item) => {
    if (item === '') {
      data.deptList = []
      return
    }
    listDept({
      deptName: item
    }).then((res) => {
      data.deptList = res.data
    })
  }

  function search(query) {
    if (query) {
      data.queryParams = { ...data.queryParams, ...query }
    }
    loading.value = true
    listWechatUser(data.queryParams)
      .then(({ data }) => {
        wechatUserList.value = data.records
        total.value = data.totalRow
        loading.value = false
      })
      .catch((err) => {
        loading.value = false
      })
  }

  // 取消按钮
  function cancel() {
    open.value = false
    reset()
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.id)
    names.value = selection.map((item) => item.openId)

    single.value = selection.length != 1
    multiple.value = !selection.length
  }

  /** 新增按钮操作 */
  function handleAdd() {
    handleClose()
    nextTick(() => {
      id.value = undefined
      open.value = true
      title.value = proxy.formatStr(proxy.$t('info.add'), proxy.$t('menu.wechatUser'))
    })
  }

  const handleClose = () => {
    open.value = false
    id.value = undefined
    title.value = ''
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    id.value = row.id || ids.value
    title.value = proxy.formatStr(proxy.$t('action.modify') + ' ' + proxy.$t('menu.wechatUser'))
    open.value = true
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const idKeys = row.id || ids.value
    const nameArr = row.openId || names.value
    proxy.$modal
      .confirm(proxy.$t('confirm.delete', { name: nameArr }))
      .then(function () {
        return delWechatUser(idKeys)
      })
      .then(() => {
        search()
        proxy.$modal.msgSuccess(proxy.$t('message.deleteSuccess'))
      })
      .catch(() => {})
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      'system/wechatUser/export',
      {
        ...queryParams.value
      },
      `wechatUser_${new Date().getTime()}.xlsx`
    )
  }

  const refreshList = (pageNum) => {
    if (pageNum) {
      data.queryParams.pageNum = 1
    }
    search()
  }

  const visibleDetail = (row) => {
    nextTick(() => {
      id.value = row.id
      title.value = proxy.formatStr(proxy.$t('action.view'), proxy.$t('menu.wechatUser'))
      detailShow.value = true
    })
  }
  /** 处理详情弹窗关闭 */
  const handleDetailClose = () => {
    detailShow.value = false
    id.value = undefined
    title.value = ''
  }
</script>
