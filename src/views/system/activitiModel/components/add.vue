<template>
  <Drawer v-model="open" @open="init">
    <template #title>
      <p>{{ title }}</p>
    </template>
    <template #btn_group>
      <el-row>
        <el-button :loading="loadingBtn.submit" type="primary" @click="submitForm"
          >{{ $t('intl.btn.submit') }}
        </el-button>
      </el-row>
    </template>

    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-form ref="activitiModelRef" :model="form" :rules="rules">
          <el-row>
            <el-col :span="12">
              <el-form-item :label="$t('activitiModel.title')" prop="title">
                <el-input
                  v-model="form.title"
                  :placeholder="$t('input.please') + ' ' + $t('activitiModel.title')"
                />
              </el-form-item>
            </el-col>

            <!--<el-col :span="12">-->
            <!--  <el-form-item :label="$t('activitiModel.modelKey')" prop="modelKey">-->
            <!--    <el-input-->
            <!--      v-model="form.activitiKey"-->
            <!--      :placeholder="$t('input.please') + ' ' + $t('activitiModel.modelKey')"-->
            <!--    />-->
            <!--  </el-form-item>-->
            <!--</el-col>-->

            <el-col :span="12">
              <el-form-item :label="$t('activitiModel.processId')" prop="processId">
                <el-input
                  v-model="form.processId"
                  :placeholder="$t('input.please') + ' ' + $t('activitiModel.processId')"
                />
              </el-form-item>
            </el-col>

            <!--<el-col :span="12">-->
            <!--  <el-form-item :label="$t('activitiModel.deployStates')" prop="deployStates">-->
            <!--    <el-select-->
            <!--      v-model="form.deployStates"-->
            <!--      :placeholder="$t('select.please') + ' ' + $t('activitiModel.deployStates')"-->
            <!--      filterable-->
            <!--      style="width: 100%"-->
            <!--    >-->
            <!--      <el-option-->
            <!--        v-for="dict in deploy_status"-->
            <!--        :key="dict.value"-->
            <!--        :label="dict.label"-->
            <!--        :value="dict.value"-->
            <!--      >-->
            <!--        <el-tag :type="dict.elTagType">-->
            <!--          {{ dict.label }}-->
            <!--        </el-tag>-->
            <!--      </el-option>-->
            <!--    </el-select>-->
            <!--  </el-form-item>-->
            <!--</el-col>-->

            <!--<el-col :span="12">-->
            <!--  <el-form-item :label="$t('activitiModel.lastDeployTime')" prop="lastDeployTime">-->
            <!--    <el-date-picker-->
            <!--      clearable-->
            <!--      style="width: 100%"-->
            <!--      v-model="form.lastDeployTime"-->
            <!--      type="date"-->
            <!--      value-format="YYYY-MM-DD"-->
            <!--      :placeholder="$t('select.please') + ' ' + $t('activitiModel.lastDeployTime')"-->
            <!--    >-->
            <!--    </el-date-picker>-->
            <!--  </el-form-item>-->
            <!--</el-col>-->
          </el-row>
        </el-form>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  const { proxy } = getCurrentInstance()
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import { listUser } from '@/api/system/user'
  import { listDept } from '@/api/system/dept'
  import { computed, toRefs } from 'vue'
  import {
    addActivitiModel,
    getActivitiModel,
    updateActivitiModel
  } from '@/api/system/activitiModel/ActivitiModel'

  const { deploy_status } = proxy.useDict('deploy_status')
  const emits = defineEmits(['update:show', 'refreshList'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: String,
      default: ''
    }
  })
  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })
  const data = reactive({
    form: {},
    rules: {
      title: [
        {
          required: true,
          message: proxy.$t('activitiModel.title') + proxy.$t('not.empty'),
          trigger: 'blur'
        }
      ],
      activitiKey: [
        {
          required: true,
          message: proxy.$t('activitiModel.activitiKey') + proxy.$t('not.empty'),
          trigger: 'blur'
        }
      ],
      processId: [
        {
          required: true,
          message: proxy.$t('activitiModel.processId') + proxy.$t('not.empty'),
          trigger: 'blur'
        }
      ],
      deployStates: [
        {
          required: true,
          message: proxy.$t('activitiModel.deployStates') + proxy.$t('not.empty'),
          trigger: 'change'
        }
      ]
    }
  })

  const { form, rules } = toRefs(data)
  const loadingBtn = reactive({
    submit: false
  })

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['activitiModelRef'].validate((valid) => {
      if (valid) {
        loadingBtn.submit = true
        if (form.value.id != null) {
          update()
        } else {
          add()
        }
      }
    })
  }

  const update = () => {
    updateActivitiModel(form.value)
      .then((response) => {
        proxy.$modal.msgSuccess(proxy.$t('intl.message.updateSuccess'))
        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }
  const add = () => {
    addActivitiModel(form.value)
      .then((response) => {
        proxy.$modal.msgSuccess(proxy.$t('common.message.addSuccess'))

        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }

  const init = () => {
    reset()
    if (props.id) {
      getActivitiModel(props.id).then((response) => {
        data.form = response.data
      })
    }
  }

  // 表单重置
  function reset() {
    form.value = {
      id: null,
      title: null,
      activitiKey: null,
      processId: null,
      deployStates: null,
      lastDeployTime: null
    }
    proxy.resetForm('activitiModelRef')
  }

  const handleUserSelect = (item) => {
    if (item === '') {
      data.userList = []
      return
    }
    listUser({
      nickName: item
    }).then((res) => {
      data.userList = res.rows
    })
  }

  const handleDeptSelect = (item) => {
    if (item === '') {
      data.deptList = []
      return
    }
    listDept({
      deptName: item
    }).then((res) => {
      data.deptList = res.data
    })
  }
</script>
