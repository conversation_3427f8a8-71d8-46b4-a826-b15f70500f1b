<template>
  <Drawer v-model="open" @open="init">
    <template #title>
      <p>{{ title }}</p>
    </template>
    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('activitiModel.title')" prop="title">
              {{ form.title }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('activitiModel.modelKey')" prop="modelKey">
              {{ form.activitiKey }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('activitiModel.processId')" prop="processId">
              {{ form.processId }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('activitiModel.deployStates')" prop="deployStates">
              <dict-tag :options="deploy_status" :value="form.deployStates" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('activitiModel.lastDeployTime')" prop="lastDeployTime">
              {{ form.lastDeployTime }}
            </el-form-item>
          </el-col>
        </el-row>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  const { proxy } = getCurrentInstance()
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'

  import { computed, toRefs } from 'vue'
  import { getActivitiModel } from '@/api/system/activitiModel/ActivitiModel'

  const { deploy_status } = proxy.useDict('deploy_status')
  const emits = defineEmits(['update:show'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: String,
      default: ''
    }
  })
  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })
  const data = reactive({
    form: {}
  })

  const { form } = toRefs(data)

  const init = () => {
    if (props.id) {
      getActivitiModel(props.id).then((response) => {
        data.form = response.data
      })
    }
  }
</script>
