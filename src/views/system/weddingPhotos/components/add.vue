<template>
  <Drawer v-model="open" @open="init">
    <template #title>
      <p>{{ title }}</p>
    </template>
    <template #btn_group>
      <el-row>
        <el-button :loading="loadingBtn.submit" type="primary" @click="submitForm"
          >{{ $t('intl.btn.submit') }}
        </el-button>
      </el-row>
    </template>

    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-form ref="weddingPhotosRef" :model="form" :rules="rules">
          <el-row>
            <!--&lt;!&ndash;类型&ndash;&gt;-->
            <!--<el-col :span="12">-->
            <!--  <el-form-item :label="$t('weddingPhotos.type')" prop="type">-->
            <!--    <el-select v-model="form.type" :placeholder="formatStr($t('select.please'),$t('weddingPhotos.type'))"-->
            <!--               filterable style="width: 100%">-->
            <!--      <el-option-->
            <!--          v-for="dict in resource_type"-->
            <!--          :key="dict.value"-->
            <!--          :label="$t(dict.type + '.' + dict.value)"-->
            <!--          :value="dict.value">-->
            <!--        <el-tag :type="dict.elTagType">-->
            <!--          {{ $t(dict.type + '.' + dict.value) }}-->
            <!--        </el-tag>-->

            <!--      </el-option>-->
            <!--    </el-select>-->
            <!--  </el-form-item>-->
            <!--</el-col>-->

            <!--链接-->
            <el-col :span="12">
              <el-form-item label="音频文件">
                <file-upload v-model="form.url" :isShowTip="false" :file-type="['mp3', 'm4a']" />
              </el-form-item>
            </el-col>

            <!--描述-->
            <!--<el-col :span="24">-->
            <!--  <el-form-item :label="$t('weddingPhotos.desc')" prop="desc">-->
            <!--    <el-input v-model="form.desc" type="textarea" :placeholder="$t('higth.input.content')"-->
            <!--              style="width: 100%"/>-->
            <!--  </el-form-item>-->
            <!--</el-col>-->

            <!--排序-->
            <!--<el-col :span="12">-->
            <!--  <el-form-item :label="$t('weddingPhotos.sortOrder')" prop="sortOrder">-->
            <!--    <el-input v-model="form.sortOrder"-->
            <!--              :placeholder="formatStr($t('input.please'),$t('weddingPhotos.sortOrder'))"/>-->
            <!--  </el-form-item>-->
            <!--</el-col>-->
          </el-row>
        </el-form>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  const { proxy } = getCurrentInstance()
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import { listUser } from '@/api/system/user'
  import { listDept } from '@/api/system/dept'
  import { computed, toRefs } from 'vue'
  import {
    addWeddingPhotos,
    getWeddingPhotos,
    updateWeddingPhotos
  } from '@/api/system/weddingPhotos/WeddingPhotos'

  const { resource_type } = proxy.useDict('resource_type')
  const emits = defineEmits(['update:show', 'refreshList'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: String,
      default: ''
    }
  })
  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })
  const data = reactive({
    form: {
      type: 'music'
    },
    rules: {}
  })

  const { form, rules } = toRefs(data)
  const loadingBtn = reactive({
    submit: false
  })

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['weddingPhotosRef'].validate((valid) => {
      if (valid) {
        loadingBtn.submit = true

        if (form.value.id != null) {
          update()
        } else {
          add()
        }
      }
    })
  }

  const update = () => {
    updateWeddingPhotos(form.value)
      .then((response) => {
        proxy.$modal.msgSuccess(proxy.$t('intl.message.updateSuccess'))
        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }
  const add = () => {
    addWeddingPhotos(form.value)
      .then((response) => {
        proxy.$modal.msgSuccess(proxy.$t('common.message.addSuccess'))

        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }

  const init = () => {
    reset()
    if (props.id) {
      getWeddingPhotos(props.id).then((response) => {
        data.form = response.data
      })
    }
  }

  // 表单重置
  function reset() {
    form.value = {
      type: 'music'
    }
    proxy.resetForm('weddingPhotosRef')
  }

  const handleUserSelect = (item) => {
    if (item === '') {
      data.userList = []
      return
    }
    listUser({
      nickName: item
    }).then((res) => {
      data.userList = res.rows
    })
  }

  const handleDeptSelect = (item) => {
    if (item === '') {
      data.deptList = []
      return
    }
    listDept({
      deptName: item
    }).then((res) => {
      data.deptList = res.data
    })
  }
</script>
