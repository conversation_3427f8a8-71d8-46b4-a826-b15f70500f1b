<template>
  <Drawer v-model="open" @open="init">
    <template #title>
      <p>{{ title }}</p>
    </template>
    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-row>
          <!--类型-->
          <el-col :span="12">
            <el-form-item :label="$t('weddingPhotos.type')" prop="type">
              <dict-tag :options="resource_type" :value="form.type" />
            </el-form-item>
          </el-col>
          <!--链接-->
          <el-col :span="12">
            <el-form-item :label="$t('weddingPhotos.url')">
              <el-image
                style="width: 100px; height: 100px"
                :src="form.url"
                :zoom-rate="1.2"
                :max-scale="7"
                :min-scale="0.2"
                :preview-src-list="[form.url]"
                :initial-index="4"
                fit="cover"
              />
            </el-form-item>
          </el-col>
          <!--描述-->
          <el-col :span="12">
            <el-form-item :label="$t('weddingPhotos.desc')" prop="desc">
              {{ form.desc }}
            </el-form-item>
          </el-col>
          <!--排序-->
          <el-col :span="12">
            <el-form-item :label="$t('weddingPhotos.sortOrder')" prop="sortOrder">
              {{ form.sortOrder }}
            </el-form-item>
          </el-col>
        </el-row>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  const { proxy } = getCurrentInstance()
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'

  import { computed, toRefs } from 'vue'
  import { getWeddingPhotos } from '@/api/system/weddingPhotos/WeddingPhotos'

  const { resource_type } = proxy.useDict('resource_type')
  const emits = defineEmits(['update:show'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: String,
      default: ''
    }
  })
  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })
  const data = reactive({
    form: {}
  })

  const { form } = toRefs(data)

  const init = () => {
    if (props.id) {
      getWeddingPhotos(props.id).then((response) => {
        data.form = response.data
      })
    }
  }
</script>
