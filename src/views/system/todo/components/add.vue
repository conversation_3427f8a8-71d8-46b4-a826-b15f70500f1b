<template>
  <Drawer v-model="open" @open="init">
    <template #title>
      <p>{{ title }}</p>
    </template>
    <template #btn_group>
      <el-row>
        <el-button :loading="loadingBtn.submit" type="primary" @click="submitForm"
          >{{ $t('intl.btn.submit') }}
        </el-button>
      </el-row>
    </template>

    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-form ref="todoRef" :model="form" :rules="rules">
          <el-row>
            <!--待办标题-->
            <el-col :span="12">
              <el-form-item :label="$t('todo.title')" prop="title">
                <el-input
                  v-model="form.title"
                  :placeholder="formatStr($t('input.please'), $t('todo.title'))"
                />
              </el-form-item>
            </el-col>

            <!--待办日期-->
            <el-col :span="12">
              <el-form-item :label="$t('todo.date')" prop="date">
                <el-date-picker
                  clearable
                  style="width: 100%"
                  v-model="form.date"
                  type="date"
                  value-format="YYYY-MM-DD"
                  :placeholder="formatStr($t('select.please'), $t('todo.date'))"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>

            <!--备注信息-->
            <el-col :span="24">
              <el-form-item :label="$t('todo.notes')" prop="notes">
                <el-input
                  v-model="form.notes"
                  type="textarea"
                  :placeholder="$t('higth.input.content')"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>

            <!--金额-->
            <el-col :span="12">
              <el-form-item :label="$t('todo.amount')" prop="amount">
                <el-input
                  v-model="form.amount"
                  :placeholder="formatStr($t('input.please'), $t('todo.amount'))"
                />
              </el-form-item>
            </el-col>

            <!--是否提醒-->
            <el-col :span="12">
              <el-form-item :label="$t('todo.needReminder')" prop="needReminder">
                <el-input
                  v-model="form.needReminder"
                  :placeholder="formatStr($t('input.please'), $t('todo.needReminder'))"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  const { proxy } = getCurrentInstance()
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import { listUser } from '@/api/system/user'
  import { listDept } from '@/api/system/dept'
  import { computed, toRefs } from 'vue'
  import { addTodo, getTodo, updateTodo } from '@/api/system/todo/Todo'

  const emits = defineEmits(['update:show', 'refreshList'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: String,
      default: ''
    }
  })
  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })
  const data = reactive({
    form: {},
    rules: {
      title: [
        {
          required: true,
          message: proxy.formatStr(proxy.$t('todo.title'), proxy.$t('not.empty')),
          trigger: ['blur', 'change']
        }
      ],
      date: [
        {
          required: true,
          message: proxy.formatStr(proxy.$t('todo.date'), proxy.$t('not.empty')),
          trigger: ['blur', 'change']
        }
      ]
    }
  })

  const { form, rules } = toRefs(data)
  const loadingBtn = reactive({
    submit: false
  })

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['todoRef'].validate((valid) => {
      if (valid) {
        loadingBtn.submit = true

        if (form.value.id != null) {
          update()
        } else {
          add()
        }
      }
    })
  }

  const update = () => {
    updateTodo(form.value)
      .then((response) => {
        proxy.$modal.msgSuccess(proxy.$t('intl.message.updateSuccess'))
        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }
  const add = () => {
    addTodo(form.value)
      .then((response) => {
        proxy.$modal.msgSuccess(proxy.$t('common.message.addSuccess'))

        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }

  const init = () => {
    reset()
    if (props.id) {
      getTodo(props.id).then((response) => {
        data.form = response.data
      })
    }
  }

  // 表单重置
  function reset() {
    form.value = {
      id: null,
      title: null,
      date: null,
      notes: null,
      amount: null,
      needReminder: null
    }
    proxy.resetForm('todoRef')
  }

  const handleUserSelect = (item) => {
    if (item === '') {
      data.userList = []
      return
    }
    listUser({
      nickName: item
    }).then((res) => {
      data.userList = res.rows
    })
  }

  const handleDeptSelect = (item) => {
    if (item === '') {
      data.deptList = []
      return
    }
    listDept({
      deptName: item
    }).then((res) => {
      data.deptList = res.data
    })
  }
</script>
