<!--运行报表-->
<template>
  <div class="page-container">
    <div class="page-header big-mb-6">
      <h1 class="page-title">{{ $t('report.running') }}</h1>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-grid big-mb-8">
      <ArtStatsCard
        icon="&#xe651;"
        :title="$t('number.tasks')"
        :count="dataInfo.jobInfoCount"
        :description="$t('tasks.running.center.scheduling')"
        icon-color="#ffffff"
        icon-bg-color="#56bdea"
        :icon-size="32"
      />
      <ArtStatsCard
        icon="&#xe751;"
        :title="$t('times.scheduling')"
        :count="dataInfo.jobLogCount"
        :description="$t('scheduling.triggers.center')"
        icon-color="#ffffff"
        icon-bg-color="#e7a03c"
        :icon-size="32"
      />
      <ArtStatsCard
        icon="&#xe810;"
        :title="$t('executor.quantity')"
        :count="dataInfo.executorCount"
        :description="$t('machines.executor.online')"
        icon-color="#ffffff"
        icon-bg-color="#4aa361"
        :icon-size="32"
      />
    </div>
    <!-- 图表区域 -->
    <div class="chart-section">
      <el-card class="chart-card">
        <template #header>
          <div class="chart-header">
            <h3 class="chart-title">{{ $t('report.scheduling') }}</h3>
            <div class="chart-controls">
              <DateSelect :show-title="true" @change="handleChange" />
            </div>
          </div>
        </template>

        <div class="chart-content">
          <div class="chart-main">
            <div ref="left" class="chart-container"></div>
          </div>
          <div class="chart-side">
            <div ref="rigth" class="chart-container"></div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { getCurrentInstance, onMounted, reactive, ref } from 'vue'
  //  按需引入 echarts
  import * as echarts from 'echarts'
  import { chartInfo } from '@/api/job'
  import DateSelect from '@/components/DateSelect/DateSelect'
  import ArtStatsCard from '@/components/core/cards/art-stats-card/index.vue'

  const { proxy } = getCurrentInstance()

  let data = reactive<{
    startDate: string
    endDate: string
  }>({ startDate: '', endDate: '' })
  let dataInfo = ref<{
    jobInfoCount: {
      type: number
      default: 0
    }
    jobLogCount: {
      type: number
      default: 0
    }
    executorCount: {
      type: number
      default: 0
    }
    triggerCountSucTotal: {
      type: number
      default: 0
    }
    triggerCountFailTotal: {
      type: number
      default: 0
    }
    triggerDayCountFailList: {
      type: Array<number>
      default: () => []
    }
    triggerCountRunningTotal: {
      type: Array<number>
      default: () => []
    }
    triggerDayList: {
      type: Array<number>
      default: () => []
    }
    triggerDayCountSucList: {
      type: Array<number>
      default: () => []
    }
    triggerDayCountRunningList: {
      type: Array<number>
      default: () => []
    }
  }>({})

  function handleChange(val) {
    if (!val) {
      timeInit()
      return
    }
    chartInfo(val).then((res) => {
      dataInfo.value = res.content
      init()
      pieChartInit()
    })
  }

  const left = ref() // 使用ref创建虚拟DOM引用，使用时用main.value
  const rigth = ref() // 使用ref创建虚拟DOM引用，使用时用main.value
  onMounted(() => {
    timeInit()
  })

  function timeInit() {
    const end = new Date()
    const start = new Date()
    start.setDate(start.getDate() - 7)
    start.setHours(0, 0, 0, 0)
    end.setHours(23, 59, 59, 0)
    let startDate = formatDate(start, 'YYYY-mm-dd HH:MM:SS')
    data.startDate = startDate
    let endDate = formatDate(end, 'YYYY-mm-dd HH:MM:SS')
    data.endDate = endDate
    chartInfo(data).then((res) => {
      dataInfo.value = res.content
      init()
      pieChartInit()
    })
  }

  /**
   * 日期格式转换
   * @param millisecond 毫秒
   * @param template 模板(可选)
   * @example formatDate(new Date(), "YYYY-mm-dd HH:MM:SS") => 2021-11-02 09:39:59
   */
  function formatDate(millisecond, template) {
    let res = ''
    try {
      let date = new Date(millisecond)
      let opt = {
        'Y+': date.getFullYear().toString(), // 年
        'm+': (date.getMonth() + 1).toString(), // 月
        'd+': date.getDate().toString(), // 日
        'H+': date.getHours().toString(), // 时
        'M+': date.getMinutes().toString(), // 分
        'S+': date.getSeconds().toString() // 秒
      }
      template = template || 'YYYY-mm-dd'
      for (let k in opt) {
        let ret = new RegExp('(' + k + ')').exec(template)
        if (ret) {
          template = template.replace(
            ret[1],
            ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, '0')
          )
        }
      }
      res = template
    } catch (error) {
      console.warn('ERROR formatDate', error)
    }
    return res
  }

  function init() {
    // 基于准备好的dom，初始化echarts实例
    let myChart = echarts.init(left.value)
    // 指定图表的配置项和数据
    let option = {
      title: {
        text: proxy.$t('chart.distribution.date')
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        }
      },
      legend: {
        data: [
          proxy.$t('status.success'),
          proxy.$t('status.failed'),
          proxy.$t('status.in.progress')
        ]
      },
      toolbox: {
        feature: {
          /*saveAsImage: {}*/
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: false,
          data: dataInfo.value.triggerDayList
        }
      ],
      yAxis: [
        {
          type: 'value'
        }
      ],
      series: [
        {
          name: proxy.$t('status.success'),
          type: 'line',
          stack: 'Total',
          areaStyle: { normal: {} },
          data: dataInfo.value.triggerDayCountSucList
        },
        {
          name: proxy.$t('status.failed'),
          type: 'line',
          stack: 'Total',
          label: {
            normal: {
              show: true,
              position: 'top'
            }
          },
          areaStyle: { normal: {} },
          data: dataInfo.value.triggerDayCountFailList
        },
        {
          name: proxy.$t('status.in.progress'),
          type: 'line',
          stack: 'Total',
          areaStyle: { normal: {} },
          data: dataInfo.value.triggerDayCountRunningList
        }
      ],
      color: ['#00A65A', '#c23632', '#F39C12']
    }
    // 使用刚指定的配置项和数据显示图表。
    myChart.setOption(option)
  }

  function pieChartInit() {
    let option = {
      title: {
        text: proxy.$t('chart.rate.success'),
        /*subtext: 'subtext',*/
        x: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{b} : {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        data: [proxy.$t('status.success'), proxy.$t('status.failed'), proxy.$t('status.running')]
      },
      series: [
        {
          //name: '分布比例',
          type: 'pie',
          radius: '55%',
          center: ['50%', '60%'],
          data: [
            {
              name: proxy.$t('status.success'),
              value: dataInfo.value.triggerCountSucTotal
            },
            {
              name: proxy.$t('status.failed'),
              value: dataInfo.value.triggerCountFailTotal
            },
            {
              name: proxy.$t('status.running'),
              value: dataInfo.value.triggerCountRunningTotal
            }
          ],
          itemStyle: {
            emphasis: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ],
      color: ['#00A65A', '#c23632', '#F39C12']
    }
    let myChart = echarts.init(rigth.value)
    myChart.setOption(option)
  }
</script>
<style lang="scss" scoped>
  .page-container {
    padding: 1.5rem;
    background-color: var(--art-bg-color);
    min-height: 100vh;
  }

  .page-header {
    .page-title {
      font-size: 1.875rem;
      font-weight: 600;
      color: var(--art-text-gray-900);
      margin: 0;
    }
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
  }

  .chart-section {
    .chart-card {
      background-color: var(--art-main-bg-color);
      border: 1px solid var(--art-border-color);
      border-radius: calc(var(--custom-radius) + 4px);
      box-shadow: var(--art-card-shadow);

      :deep(.el-card__header) {
        padding: 1.5rem;
        border-bottom: 1px solid var(--art-border-color);
      }

      :deep(.el-card__body) {
        padding: 1.5rem;
      }
    }

    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 1rem;

      .chart-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--art-text-gray-900);
        margin: 0;
      }

      .chart-controls {
        display: flex;
        align-items: center;
        gap: 0.75rem;
      }
    }

    .chart-content {
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: 1.5rem;
      min-height: 400px;

      @media (max-width: 1024px) {
        grid-template-columns: 1fr;
        gap: 1rem;
      }

      .chart-main,
      .chart-side {
        display: flex;
        flex-direction: column;
      }

      .chart-container {
        width: 100%;
        height: 400px;
        border-radius: calc(var(--custom-radius) / 2);
        background-color: var(--art-bg-color);
        border: 1px solid var(--art-border-color);
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        @media (max-width: 1024px) {
          height: 300px;
        }

        @media (max-width: 768px) {
          height: 250px;
        }
      }
    }
  }

  // 响应式优化
  @media (max-width: 768px) {
    .page-container {
      padding: 1rem;
    }

    .page-header {
      .page-title {
        font-size: 1.5rem;
      }
    }
  }
</style>
