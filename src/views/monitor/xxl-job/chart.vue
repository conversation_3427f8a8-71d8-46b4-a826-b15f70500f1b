<!--运行报表-->
<template>
  <div class="big-p-6 big-bg-gray-50 big-min-h-screen">
    <div class="big-mb-6">
      <h1 class="big-text-3xl big-font-semibold big-text-gray-900 big-m-0">{{ $t('report.running') }}</h1>
    </div>

    <!-- 统计卡片区域 -->
    <div class="big-grid big-grid-cols-1 md:big-grid-cols-2 lg:big-grid-cols-3 big-gap-6 big-mb-8">
      <ArtStatsCard
        icon="&#xe651;"
        :title="$t('number.tasks')"
        :count="dataInfo.jobInfoCount"
        :description="$t('tasks.running.center.scheduling')"
        variant="primary"
        icon-style="rounded"
        icon-color="#ffffff"
        icon-bg-color="#56bdea"
        :icon-size="32"
        :trend="8.5"
        :progress="75"
        clickable
        show-decoration
        @click="handleCardClick('tasks')"
      />
      <ArtStatsCard
        icon="&#xe751;"
        :title="$t('times.scheduling')"
        :count="dataInfo.jobLogCount"
        :description="$t('scheduling.triggers.center')"
        variant="warning"
        icon-style="circle"
        icon-color="#ffffff"
        icon-bg-color="#e7a03c"
        :icon-size="32"
        :trend="-2.3"
        :progress="60"
        unit="次"
        clickable
        show-decoration
        @click="handleCardClick('scheduling')"
      />
      <ArtStatsCard
        icon="&#xe810;"
        :title="$t('executor.quantity')"
        :count="dataInfo.executorCount"
        :description="$t('machines.executor.online')"
        variant="success"
        icon-style="rounded"
        icon-color="#ffffff"
        icon-bg-color="#4aa361"
        :icon-size="32"
        :trend="12.8"
        :progress="90"
        unit="台"
        clickable
        show-decoration
        @click="handleCardClick('executor')"
      />
    </div>
    <!-- 图表区域 -->
    <div class="chart-section">
      <el-card class="chart-card">
        <template #header>
          <div class="chart-header">
            <h3 class="chart-title">{{ $t('report.scheduling') }}</h3>
            <div class="chart-controls">
              <DateSelect :show-title="true" @change="handleChange" />
            </div>
          </div>
        </template>

        <div class="chart-content">
          <div class="chart-main">
            <div ref="left" class="chart-container">
              <div class="chart-loading" v-if="loading">
                <el-icon class="is-loading"><Loading /></el-icon>
                <span>加载中...</span>
              </div>
            </div>
          </div>
          <div class="chart-side">
            <div ref="rigth" class="chart-container">
              <div class="chart-loading" v-if="loading">
                <el-icon class="is-loading"><Loading /></el-icon>
                <span>加载中...</span>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { getCurrentInstance, onMounted, onUnmounted, reactive, ref } from 'vue'
  import { Loading } from '@element-plus/icons-vue'
  //  按需引入 echarts
  import * as echarts from 'echarts'
  import { chartInfo } from '@/api/job'
  import DateSelect from '@/components/DateSelect/DateSelect'
  import ArtStatsCard from '@/components/core/cards/art-stats-card/index.vue'

  const { proxy } = getCurrentInstance()

  let data = reactive<{
    startDate: string
    endDate: string
  }>({ startDate: '', endDate: '' })
  // 模拟数据 - 用于展示UI效果
  let dataInfo = ref({
    jobInfoCount: 156,
    jobLogCount: 8924,
    executorCount: 12,
    triggerCountSucTotal: 7856,
    triggerCountFailTotal: 892,
    triggerCountRunningTotal: 176,
    triggerDayList: ['01-25', '01-26', '01-27', '01-28', '01-29', '01-30', '01-31'],
    triggerDayCountSucList: [1200, 1350, 1180, 1420, 1680, 1520, 1380],
    triggerDayCountFailList: [120, 95, 140, 85, 110, 130, 105],
    triggerDayCountRunningList: [25, 30, 18, 35, 28, 22, 26]
  })

  function handleChange(val) {
    if (!val) {
      timeInit()
      return
    }

    // 模拟数据变化效果
    console.log('日期选择变化:', val)

    // 模拟随机数据变化
    dataInfo.value.triggerDayCountSucList = dataInfo.value.triggerDayCountSucList.map(
      () => Math.floor(Math.random() * 1000) + 800
    )
    dataInfo.value.triggerDayCountFailList = dataInfo.value.triggerDayCountFailList.map(
      () => Math.floor(Math.random() * 200) + 50
    )
    dataInfo.value.triggerDayCountRunningList = dataInfo.value.triggerDayCountRunningList.map(
      () => Math.floor(Math.random() * 50) + 10
    )

    // 重新渲染图表
    init()
    pieChartInit()

    // 原有API调用逻辑（暂时注释）
    // chartInfo(val).then((res) => {
    //   dataInfo.value = res.content
    //   init()
    //   pieChartInit()
    // })
  }

  const left = ref() // 使用ref创建虚拟DOM引用，使用时用main.value
  const rigth = ref() // 使用ref创建虚拟DOM引用，使用时用main.value
  const loading = ref(false) // 加载状态

  // 图表实例
  let leftChart = null
  let rightChart = null

  // 获取主题色的工具函数
  const getThemeColors = () => {
    const primaryColor = getComputedStyle(document.documentElement)
      .getPropertyValue('--el-color-primary')
      .trim()
    const successColor = getComputedStyle(document.documentElement)
      .getPropertyValue('--el-color-success')
      .trim()
    const warningColor = getComputedStyle(document.documentElement)
      .getPropertyValue('--el-color-warning')
      .trim()
    const errorColor = getComputedStyle(document.documentElement)
      .getPropertyValue('--el-color-error')
      .trim()
    const textColor = getComputedStyle(document.documentElement)
      .getPropertyValue('--art-text-gray-900')
      .trim()
    const textSecondaryColor = getComputedStyle(document.documentElement)
      .getPropertyValue('--art-text-gray-600')
      .trim()

    return {
      primary: primaryColor || '#409EFF',
      success: successColor || '#67C23A',
      warning: warningColor || '#E6A23C',
      error: errorColor || '#F56C6C',
      text: textColor || '#303133',
      textSecondary: textSecondaryColor || '#606266',
      modernColors: [
        successColor || '#67C23A', // 成功 - 绿色
        errorColor || '#F56C6C', // 失败 - 红色
        primaryColor || '#409EFF' // 运行中 - 主题色
      ]
    }
  }

  // 获取响应式配置
  const getResponsiveConfig = () => {
    const width = window.innerWidth

    if (width <= 480) {
      return {
        titleFontSize: 14,
        legendFontSize: 11,
        labelFontSize: 10,
        gridLeft: '8%',
        gridRight: '8%',
        gridTop: '20%',
        gridBottom: '15%',
        // 饼图配置
        pieRadius: ['30%', '55%'],
        pieCenter: ['50%', '50%'],
        legendOrient: 'horizontal',
        legendPosition: { left: 'center', bottom: 10 }
      }
    } else if (width <= 768) {
      return {
        titleFontSize: 15,
        legendFontSize: 12,
        labelFontSize: 11,
        gridLeft: '6%',
        gridRight: '6%',
        gridTop: '18%',
        gridBottom: '12%',
        // 饼图配置
        pieRadius: ['32%', '60%'],
        pieCenter: ['50%', '52%'],
        legendOrient: 'horizontal',
        legendPosition: { left: 'center', bottom: 15 }
      }
    } else if (width <= 1024) {
      return {
        titleFontSize: 16,
        legendFontSize: 13,
        labelFontSize: 12,
        gridLeft: '4%',
        gridRight: '4%',
        gridTop: '16%',
        gridBottom: '10%',
        // 饼图配置
        pieRadius: ['35%', '65%'],
        pieCenter: ['55%', '55%'],
        legendOrient: 'vertical',
        legendPosition: { left: 'left', top: 'middle' }
      }
    } else {
      return {
        titleFontSize: 16,
        legendFontSize: 14,
        labelFontSize: 12,
        gridLeft: '3%',
        gridRight: '4%',
        gridTop: '15%',
        gridBottom: '8%',
        // 饼图配置
        pieRadius: ['35%', '65%'],
        pieCenter: ['55%', '55%'],
        legendOrient: 'vertical',
        legendPosition: { left: 'left', top: 'middle' }
      }
    }
  }
  onMounted(() => {
    // 模拟加载过程
    loading.value = true

    // 使用模拟数据直接初始化图表
    setTimeout(() => {
      init()
      pieChartInit()
      loading.value = false
    }, 800) // 模拟加载时间

    // 监听窗口大小变化
    const handleResize = () => {
      if (leftChart) {
        leftChart.resize()
      }
      if (rightChart) {
        rightChart.resize()
      }
    }

    // 防抖处理，避免频繁触发
    let resizeTimer = null
    const debouncedResize = () => {
      if (resizeTimer) {
        clearTimeout(resizeTimer)
      }
      resizeTimer = setTimeout(handleResize, 150)
    }

    // 添加窗口大小变化监听器
    window.addEventListener('resize', debouncedResize)

    // 使用 ResizeObserver 监听容器大小变化（更精确）
    let resizeObserver = null
    if (window.ResizeObserver) {
      resizeObserver = new ResizeObserver((entries) => {
        for (let entry of entries) {
          if (entry.target === left.value || entry.target === rigth.value) {
            debouncedResize()
            break
          }
        }
      })

      // 监听图表容器
      setTimeout(() => {
        if (left.value) resizeObserver.observe(left.value)
        if (rigth.value) resizeObserver.observe(rigth.value)
      }, 100)
    }

    // 监听主题变化，重新渲染图表
    const themeObserver = new MutationObserver(() => {
      // 延迟一点时间让主题切换完成
      setTimeout(() => {
        if (left.value && rigth.value) {
          init()
          pieChartInit()
        }
      }, 100)
    })

    // 监听 html 元素的 class 变化（主题切换）
    themeObserver.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class', 'data-theme']
    })

    // 组件卸载时清理监听器
    onUnmounted(() => {
      window.removeEventListener('resize', debouncedResize)
      themeObserver.disconnect()

      // 清理 ResizeObserver
      if (resizeObserver) {
        resizeObserver.disconnect()
      }

      // 销毁图表实例
      if (leftChart) {
        leftChart.dispose()
        leftChart = null
      }
      if (rightChart) {
        rightChart.dispose()
        rightChart = null
      }

      // 清理定时器
      if (resizeTimer) {
        clearTimeout(resizeTimer)
      }
    })

    // 原有的API调用逻辑（暂时注释）
    // timeInit()
  })

  function timeInit() {
    const end = new Date()
    const start = new Date()
    start.setDate(start.getDate() - 7)
    start.setHours(0, 0, 0, 0)
    end.setHours(23, 59, 59, 0)
    let startDate = formatDate(start, 'YYYY-mm-dd HH:MM:SS')
    data.startDate = startDate
    let endDate = formatDate(end, 'YYYY-mm-dd HH:MM:SS')
    data.endDate = endDate

    // 使用模拟数据替代API调用
    // chartInfo(data).then((res) => {
    //   dataInfo.value = res.content
    //   init()
    //   pieChartInit()
    // })

    // 直接使用模拟数据
    init()
    pieChartInit()
  }

  /**
   * 日期格式转换
   * @param millisecond 毫秒
   * @param template 模板(可选)
   * @example formatDate(new Date(), "YYYY-mm-dd HH:MM:SS") => 2021-11-02 09:39:59
   */
  function formatDate(millisecond, template) {
    let res = ''
    try {
      let date = new Date(millisecond)
      let opt = {
        'Y+': date.getFullYear().toString(), // 年
        'm+': (date.getMonth() + 1).toString(), // 月
        'd+': date.getDate().toString(), // 日
        'H+': date.getHours().toString(), // 时
        'M+': date.getMinutes().toString(), // 分
        'S+': date.getSeconds().toString() // 秒
      }
      template = template || 'YYYY-mm-dd'
      for (let k in opt) {
        let ret = new RegExp('(' + k + ')').exec(template)
        if (ret) {
          template = template.replace(
            ret[1],
            ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, '0')
          )
        }
      }
      res = template
    } catch (error) {
      console.warn('ERROR formatDate', error)
    }
    return res
  }

  function init() {
    // 销毁旧的图表实例
    if (leftChart) {
      leftChart.dispose()
    }

    // 基于准备好的dom，初始化echarts实例
    leftChart = echarts.init(left.value)

    // 获取主题色和响应式配置
    const colors = getThemeColors()
    const responsive = getResponsiveConfig()

    // 指定图表的配置项和数据
    let option = {
      title: {
        text: proxy.$t('chart.distribution.date'),
        textStyle: {
          color: colors.text,
          fontSize: responsive.titleFontSize,
          fontWeight: 500
        },
        left: 'left',
        top: 10
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(50, 50, 50, 0.9)',
        borderColor: 'transparent',
        textStyle: {
          color: '#fff'
        },
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: colors.primary
          },
          label: {
            backgroundColor: colors.primary
          }
        }
      },
      legend: {
        data: [
          proxy.$t('status.success'),
          proxy.$t('status.failed'),
          proxy.$t('status.in.progress')
        ],
        top: 10,
        right: 'right',
        textStyle: {
          color: colors.textSecondary,
          fontSize: responsive.legendFontSize
        }
      },
      grid: {
        left: responsive.gridLeft,
        right: responsive.gridRight,
        bottom: responsive.gridBottom,
        top: responsive.gridTop,
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: false,
          data: dataInfo.value.triggerDayList,
          axisLine: {
            lineStyle: {
              color: colors.textSecondary
            }
          },
          axisLabel: {
            color: colors.textSecondary
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          axisLine: {
            lineStyle: {
              color: colors.textSecondary
            }
          },
          axisLabel: {
            color: colors.textSecondary
          },
          splitLine: {
            lineStyle: {
              color: colors.textSecondary,
              type: 'dashed',
              opacity: 0.3
            }
          }
        }
      ],
      series: [
        {
          name: proxy.$t('status.success'),
          type: 'line',
          stack: 'Total',
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            width: 3
          },
          areaStyle: {
            opacity: 0.6,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: colors.modernColors[0] },
              { offset: 1, color: colors.success + '20' }
            ])
          },
          data: dataInfo.value.triggerDayCountSucList
        },
        {
          name: proxy.$t('status.failed'),
          type: 'line',
          stack: 'Total',
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            width: 3
          },
          areaStyle: {
            opacity: 0.6,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: colors.modernColors[1] },
              { offset: 1, color: colors.error + '20' }
            ])
          },
          data: dataInfo.value.triggerDayCountFailList
        },
        {
          name: proxy.$t('status.in.progress'),
          type: 'line',
          stack: 'Total',
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            width: 3
          },
          areaStyle: {
            opacity: 0.6,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: colors.modernColors[2] },
              { offset: 1, color: colors.primary + '20' }
            ])
          },
          data: dataInfo.value.triggerDayCountRunningList
        }
      ],
      color: colors.modernColors
    }
    // 使用刚指定的配置项和数据显示图表。
    leftChart.setOption(option)

    // 启用图表的响应式
    leftChart.resize()
  }

  function pieChartInit() {
    // 销毁旧的图表实例
    if (rightChart) {
      rightChart.dispose()
    }

    // 获取主题色和响应式配置
    const colors = getThemeColors()
    const responsive = getResponsiveConfig()

    let option = {
      title: {
        text: proxy.$t('chart.rate.success'),
        left: 'center',
        top: 15,
        textStyle: {
          color: colors.text,
          fontSize: responsive.titleFontSize,
          fontWeight: 500
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)',
        backgroundColor: 'rgba(50, 50, 50, 0.9)',
        borderColor: 'transparent',
        textStyle: {
          color: '#fff'
        }
      },
      legend: {
        orient: responsive.legendOrient,
        ...responsive.legendPosition,
        data: [proxy.$t('status.success'), proxy.$t('status.failed'), proxy.$t('status.running')],
        textStyle: {
          color: colors.textSecondary,
          fontSize: responsive.legendFontSize
        },
        itemWidth: 12,
        itemHeight: 12,
        itemGap: responsive.legendOrient === 'horizontal' ? 20 : 16
      },
      series: [
        {
          type: 'pie',
          radius: responsive.pieRadius,
          center: responsive.pieCenter,
          avoidLabelOverlap: false,
          emphasis: {
            scale: true,
            scaleSize: 5,
            itemStyle: {
              shadowBlur: 20,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.3)'
            }
          },
          label: {
            show: window.innerWidth > 768, // 小屏幕隐藏标签，避免拥挤
            position: 'outside',
            formatter: '{d}%',
            fontSize: responsive.labelFontSize,
            color: colors.textSecondary
          },
          labelLine: {
            show: window.innerWidth > 768,
            length: window.innerWidth > 1024 ? 15 : 10,
            length2: window.innerWidth > 1024 ? 10 : 5,
            lineStyle: {
              color: colors.textSecondary
            }
          },
          data: [
            {
              name: proxy.$t('status.success'),
              value: dataInfo.value.triggerCountSucTotal,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: colors.modernColors[0] },
                  { offset: 1, color: colors.success + 'CC' }
                ])
              }
            },
            {
              name: proxy.$t('status.failed'),
              value: dataInfo.value.triggerCountFailTotal,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: colors.modernColors[1] },
                  { offset: 1, color: colors.error + 'CC' }
                ])
              }
            },
            {
              name: proxy.$t('status.running'),
              value: dataInfo.value.triggerCountRunningTotal,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: colors.modernColors[2] },
                  { offset: 1, color: colors.primary + 'CC' }
                ])
              }
            }
          ]
        }
      ]
    }
    rightChart = echarts.init(rigth.value)
    rightChart.setOption(option)

    // 启用图表的响应式
    rightChart.resize()
  }
</script>
<style lang="scss" scoped>
  .page-container {
    padding: 1.5rem;
    background-color: var(--art-bg-color);
    min-height: 100vh;
  }

  .page-header {
    .page-title {
      font-size: 1.875rem;
      font-weight: 600;
      color: var(--art-text-gray-900);
      margin: 0;
    }
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
  }

  .chart-section {
    .chart-card {
      background-color: var(--art-main-bg-color);
      border: 1px solid var(--art-border-color);
      border-radius: calc(var(--custom-radius) + 4px);
      box-shadow: var(--art-card-shadow);

      :deep(.el-card__header) {
        padding: 1.5rem;
        border-bottom: 1px solid var(--art-border-color);
      }

      :deep(.el-card__body) {
        padding: 1.5rem;
      }
    }

    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 1rem;

      .chart-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--art-text-gray-900);
        margin: 0;
      }

      .chart-controls {
        display: flex;
        align-items: center;
        gap: 0.75rem;
      }
    }

    .chart-content {
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: 1.5rem;
      min-height: 400px;

      @media (max-width: 1024px) {
        grid-template-columns: 1fr;
        gap: 1rem;
        min-height: auto;
      }

      .chart-main,
      .chart-side {
        display: flex;
        flex-direction: column;
        min-width: 0; // 防止内容溢出
      }

      .chart-container {
        width: 100%;
        height: 400px;
        min-height: 300px;
        border-radius: calc(var(--custom-radius) / 2);
        background-color: var(--art-main-bg-color);
        border: 1px solid var(--art-border-color);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        // 确保图表在暗色主题下正常显示
        :deep(canvas) {
          border-radius: calc(var(--custom-radius) / 2);
          width: 100% !important;
          height: 100% !important;
        }

        // 确保 ECharts 容器正确响应
        :deep(.echarts-for-react) {
          width: 100% !important;
          height: 100% !important;
        }

        @media (max-width: 1200px) {
          height: 380px;
        }

        @media (max-width: 1024px) {
          height: 350px;
        }

        @media (max-width: 768px) {
          height: 320px;
          min-height: 280px;
        }

        @media (max-width: 480px) {
          height: 280px;
          min-height: 250px;
        }

        // 饼图容器特殊处理
        &:last-child {
          @media (max-width: 1024px) {
            height: 380px; // 饼图需要更多空间放置图例
          }

          @media (max-width: 768px) {
            height: 350px;
          }

          @media (max-width: 480px) {
            height: 320px;
          }
        }

        .chart-loading {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 0.5rem;
          color: var(--art-text-gray-600);
          font-size: 14px;
          z-index: 10;

          .el-icon {
            font-size: 24px;
            color: var(--el-color-primary);
          }
        }
      }
    }
  }

  // 响应式优化
  @media (max-width: 768px) {
    .page-container {
      padding: 1rem;
    }

    .page-header {
      .page-title {
        font-size: 1.5rem;
      }
    }
  }
</style>
