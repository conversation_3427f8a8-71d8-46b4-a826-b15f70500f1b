<!-- 增强版统计卡片 - 基于 ArtStatsCard 优化 -->
<template>
  <div 
    class="enhanced-stats-card art-custom-card" 
    :class="[
      `enhanced-stats-card--${variant}`,
      { 'enhanced-stats-card--clickable': clickable }
    ]"
    :style="cardStyle"
    @click="handleClick"
  >
    <!-- 背景装饰 -->
    <div class="enhanced-stats-card__bg-decoration" v-if="showDecoration">
      <div class="decoration-circle decoration-circle--1"></div>
      <div class="decoration-circle decoration-circle--2"></div>
      <div class="decoration-circle decoration-circle--3"></div>
    </div>

    <!-- 主要内容区域 -->
    <div class="enhanced-stats-card__content">
      <!-- 图标区域 -->
      <div 
        v-if="icon" 
        class="enhanced-stats-card__icon"
        :class="[`enhanced-stats-card__icon--${iconVariant}`]"
        :style="iconStyle"
      >
        <i
          class="iconfont-sys"
          v-html="icon"
          :style="{ fontSize: iconSize + 'px' }"
        ></i>
      </div>

      <!-- 文本内容区域 -->
      <div class="enhanced-stats-card__text">
        <!-- 标题 -->
        <p v-if="title" class="enhanced-stats-card__title">
          {{ title }}
        </p>

        <!-- 数值显示 -->
        <div class="enhanced-stats-card__value-container">
          <ArtCountTo
            v-if="count !== undefined"
            class="enhanced-stats-card__count"
            :target="count"
            :duration="animationDuration"
            :separator="separator"
            :prefix="prefix"
            :suffix="suffix"
            :decimals="decimals"
          />
          <span v-if="unit" class="enhanced-stats-card__unit">{{ unit }}</span>
        </div>

        <!-- 趋势指示器 -->
        <div v-if="trend !== undefined" class="enhanced-stats-card__trend">
          <i 
            class="iconfont-sys enhanced-stats-card__trend-icon"
            :class="trendClass"
            v-html="trendIcon"
          ></i>
          <span class="enhanced-stats-card__trend-text">
            {{ Math.abs(trend) }}%
          </span>
          <span class="enhanced-stats-card__trend-label">{{ trendLabel }}</span>
        </div>

        <!-- 描述文本 -->
        <p v-if="description" class="enhanced-stats-card__description">
          {{ description }}
        </p>
      </div>

      <!-- 右侧操作区域 -->
      <div v-if="showArrow || $slots.action" class="enhanced-stats-card__action">
        <slot name="action">
          <i v-if="showArrow" class="iconfont-sys enhanced-stats-card__arrow">&#xe703;</i>
        </slot>
      </div>
    </div>

    <!-- 底部进度条 -->
    <div v-if="progress !== undefined" class="enhanced-stats-card__progress">
      <div 
        class="enhanced-stats-card__progress-bar"
        :style="{ width: progress + '%', backgroundColor: progressColor }"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import ArtCountTo from '@/components/core/text-effect/art-count-to/index.vue'

  defineOptions({ name: 'ArtEnhancedStatsCard' })

  interface EnhancedStatsCardProps {
    /** 图标 */
    icon?: string
    /** 标题 */
    title?: string
    /** 数值 */
    count?: number
    /** 描述 */
    description?: string
    /** 卡片变体 */
    variant?: 'default' | 'primary' | 'success' | 'warning' | 'error' | 'gradient'
    /** 图标变体 */
    iconVariant?: 'circle' | 'square' | 'rounded' | 'none'
    /** 图标大小 */
    iconSize?: number
    /** 图标颜色 */
    iconColor?: string
    /** 图标背景颜色 */
    iconBgColor?: string
    /** 背景颜色 */
    backgroundColor?: string
    /** 是否显示箭头 */
    showArrow?: boolean
    /** 是否可点击 */
    clickable?: boolean
    /** 是否显示装饰元素 */
    showDecoration?: boolean
    /** 动画持续时间 */
    animationDuration?: number
    /** 数字分隔符 */
    separator?: string
    /** 前缀 */
    prefix?: string
    /** 后缀 */
    suffix?: string
    /** 小数位数 */
    decimals?: number
    /** 单位 */
    unit?: string
    /** 趋势百分比 */
    trend?: number
    /** 趋势标签 */
    trendLabel?: string
    /** 进度百分比 */
    progress?: number
    /** 进度条颜色 */
    progressColor?: string
  }

  interface EnhancedStatsCardEmits {
    click: [event: MouseEvent]
  }

  const props = withDefaults(defineProps<EnhancedStatsCardProps>(), {
    variant: 'default',
    iconVariant: 'circle',
    iconSize: 32,
    animationDuration: 2000,
    separator: ',',
    decimals: 0,
    trendLabel: '较上期'
  })

  const emit = defineEmits<EnhancedStatsCardEmits>()

  // 获取主题色
  const getThemeColor = (colorName: string) => {
    return getComputedStyle(document.documentElement)
      .getPropertyValue(`--el-color-${colorName}`)
      .trim() || '#409EFF'
  }

  // 卡片样式
  const cardStyle = computed(() => {
    const styles: Record<string, string> = {}
    
    if (props.backgroundColor) {
      styles.backgroundColor = props.backgroundColor
    }

    return styles
  })

  // 图标样式
  const iconStyle = computed(() => {
    const styles: Record<string, string> = {}
    
    if (props.iconColor) {
      styles.color = props.iconColor
    }
    
    if (props.iconBgColor) {
      styles.backgroundColor = props.iconBgColor
    }

    return styles
  })

  // 趋势相关计算
  const trendClass = computed(() => {
    if (props.trend === undefined) return ''
    return props.trend >= 0 ? 'trend-up' : 'trend-down'
  })

  const trendIcon = computed(() => {
    if (props.trend === undefined) return ''
    return props.trend >= 0 ? '&#xe7e8;' : '&#xe7e9;' // 上升/下降箭头图标
  })

  // 进度条颜色
  const progressColor = computed(() => {
    if (props.progressColor) return props.progressColor
    
    switch (props.variant) {
      case 'primary': return getThemeColor('primary')
      case 'success': return getThemeColor('success')
      case 'warning': return getThemeColor('warning')
      case 'error': return getThemeColor('error')
      default: return getThemeColor('primary')
    }
  })

  // 点击处理
  const handleClick = (event: MouseEvent) => {
    if (props.clickable) {
      emit('click', event)
    }
  }
</script>

<style lang="scss" scoped>
  .enhanced-stats-card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-height: 8rem;
    padding: 1.5rem;
    background-color: var(--art-main-bg-color);
    border: 1px solid var(--art-border-color);
    border-radius: calc(var(--custom-radius) + 6px);
    box-shadow: var(--art-card-shadow);
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    // 可点击状态
    &--clickable {
      cursor: pointer;
      
      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
      }

      &:active {
        transform: translateY(-1px);
      }
    }

    // 变体样式
    &--primary {
      background: linear-gradient(135deg, var(--el-color-primary-light-9) 0%, var(--el-color-primary-light-8) 100%);
      border-color: var(--el-color-primary-light-7);
    }

    &--success {
      background: linear-gradient(135deg, var(--el-color-success-light-9) 0%, var(--el-color-success-light-8) 100%);
      border-color: var(--el-color-success-light-7);
    }

    &--warning {
      background: linear-gradient(135deg, var(--el-color-warning-light-9) 0%, var(--el-color-warning-light-8) 100%);
      border-color: var(--el-color-warning-light-7);
    }

    &--error {
      background: linear-gradient(135deg, var(--el-color-error-light-9) 0%, var(--el-color-error-light-8) 100%);
      border-color: var(--el-color-error-light-7);
    }

    &--gradient {
      background: linear-gradient(135deg, 
        var(--el-color-primary-light-9) 0%, 
        var(--el-color-success-light-9) 50%, 
        var(--el-color-warning-light-9) 100%
      );
      border: none;
    }

    // 背景装饰
    &__bg-decoration {
      position: absolute;
      top: 0;
      right: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      opacity: 0.1;

      .decoration-circle {
        position: absolute;
        border-radius: 50%;
        background: var(--el-color-primary);

        &--1 {
          width: 60px;
          height: 60px;
          top: -20px;
          right: -20px;
        }

        &--2 {
          width: 40px;
          height: 40px;
          top: 20px;
          right: 10px;
        }

        &--3 {
          width: 20px;
          height: 20px;
          bottom: 10px;
          right: 30px;
        }
      }
    }

    // 主要内容
    &__content {
      display: flex;
      align-items: flex-start;
      gap: 1rem;
      flex: 1;
    }

    // 图标区域
    &__icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 3rem;
      height: 3rem;
      flex-shrink: 0;
      color: #fff;
      background-color: var(--el-color-primary);
      transition: all 0.3s ease;

      &--circle {
        border-radius: 50%;
      }

      &--square {
        border-radius: 4px;
      }

      &--rounded {
        border-radius: 12px;
      }

      &--none {
        background: none;
        color: var(--art-text-gray-600);
      }
    }

    // 文本区域
    &__text {
      flex: 1;
      min-width: 0;
    }

    &__title {
      margin: 0 0 0.5rem 0;
      font-size: 0.875rem;
      font-weight: 500;
      color: var(--art-text-gray-600);
      line-height: 1.2;
    }

    &__value-container {
      display: flex;
      align-items: baseline;
      gap: 0.25rem;
      margin-bottom: 0.5rem;
    }

    &__count {
      font-size: 1.875rem;
      font-weight: 600;
      color: var(--art-text-gray-900);
      line-height: 1;
    }

    &__unit {
      font-size: 1rem;
      font-weight: 500;
      color: var(--art-text-gray-600);
    }

    // 趋势指示器
    &__trend {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      margin-bottom: 0.5rem;
      font-size: 0.75rem;

      &-icon {
        font-size: 0.875rem;

        &.trend-up {
          color: var(--el-color-success);
        }

        &.trend-down {
          color: var(--el-color-error);
        }
      }

      &-text {
        font-weight: 600;
        
        .trend-up & {
          color: var(--el-color-success);
        }

        .trend-down & {
          color: var(--el-color-error);
        }
      }

      &-label {
        color: var(--art-text-gray-500);
      }
    }

    &__description {
      margin: 0;
      font-size: 0.75rem;
      color: var(--art-text-gray-500);
      line-height: 1.4;
    }

    // 操作区域
    &__action {
      display: flex;
      align-items: center;
      flex-shrink: 0;
    }

    &__arrow {
      font-size: 1.125rem;
      color: var(--art-text-gray-400);
      transition: all 0.3s ease;

      .enhanced-stats-card:hover & {
        color: var(--el-color-primary);
        transform: translateX(2px);
      }
    }

    // 进度条
    &__progress {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 3px;
      background-color: var(--art-border-color);
      border-radius: 0 0 calc(var(--custom-radius) + 6px) calc(var(--custom-radius) + 6px);
      overflow: hidden;

      &-bar {
        height: 100%;
        background-color: var(--el-color-primary);
        transition: width 0.8s ease;
        border-radius: inherit;
      }
    }

    // 响应式设计
    @media (max-width: 768px) {
      padding: 1rem;
      min-height: 6rem;

      &__icon {
        width: 2.5rem;
        height: 2.5rem;
      }

      &__count {
        font-size: 1.5rem;
      }
    }
  }
</style>
