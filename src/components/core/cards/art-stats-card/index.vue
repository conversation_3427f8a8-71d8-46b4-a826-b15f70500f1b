<!-- 统计卡片 - Tailwind CSS 版本 -->
<template>
  <div
    class="art-custom-card big-relative big-flex big-flex-col big-min-h-32 big-p-6 big-transition-all big-duration-300 big-ease-in-out hover:big-transform hover:big--translate-y-1 hover:big-shadow-lg"
    :class="[
      variantClasses,
      {
        'big-cursor-pointer hover:big--translate-y-2 hover:big-shadow-xl active:big--translate-y-0.5': clickable,
        'big-bg-gradient-to-br': variant === 'gradient'
      }
    ]"
    :style="cardStyle"
    @click="handleClick"
  >
    <!-- 背景装饰元素 -->
    <div v-if="showDecoration" class="big-absolute big-top-0 big-right-0 big-w-full big-h-full big-pointer-events-none big-opacity-8">
      <div class="big-absolute big-w-20 big-h-20 big-bg-blue-500 big-rounded-full big--top-5 big--right-5"></div>
      <div class="big-absolute big-w-12 big-h-12 big-bg-blue-400 big-rounded-full big-top-5 big-right-2.5"></div>
      <div class="big-absolute big-w-8 big-h-8 big-bg-blue-300 big-rounded-full big-bottom-2.5 big-right-7.5"></div>
    </div>

    <!-- 主要内容区域 -->
    <div class="big-flex big-items-start big-gap-4 big-flex-1 big-relative big-z-10">
      <!-- 图标区域 -->
      <div
        v-if="icon"
        class="big-flex big-items-center big-justify-center big-w-14 big-h-14 big-flex-shrink-0 big-text-white big-transition-all big-duration-300 hover:big-scale-105"
        :class="iconStyleClasses"
        :style="iconContainerStyle"
      >
        <i
          class="iconfont-sys"
          v-html="icon"
          :style="{ fontSize: iconSize + 'px', color: iconColor }"
        ></i>
      </div>

      <!-- 内容区域 -->
      <div class="big-flex-1 big-min-w-0">
        <!-- 标题和趋势 -->
        <div class="big-flex big-justify-between big-items-start big-mb-3">
          <p v-if="title" class="big-m-0 big-text-sm big-font-medium big-text-gray-600 big-leading-tight">
            {{ title }}
          </p>
          <div v-if="trend !== undefined" class="big-flex big-items-center big-gap-1 big-text-xs">
            <i
              class="iconfont-sys big-text-sm"
              :class="trendClass"
              v-html="trendIcon"
            ></i>
            <span class="big-font-semibold" :class="trendClass">
              {{ Math.abs(trend) }}%
            </span>
          </div>
        </div>

        <!-- 数值显示 -->
        <div class="big-flex big-items-baseline big-gap-2 big-mb-2">
          <ArtCountTo
            v-if="count !== undefined"
            class="big-text-2xl big-font-bold big-text-gray-900 big-leading-none"
            :target="count"
            :duration="animationDuration"
            :separator="separator"
            :prefix="prefix"
            :suffix="suffix"
            :decimals="decimals"
          />
          <span v-if="unit" class="big-text-base big-font-medium big-text-gray-600">
            {{ unit }}
          </span>
        </div>

        <!-- 描述文本 -->
        <p v-if="description" class="big-m-0 big-text-xs big-text-gray-500 big-leading-relaxed">
          {{ description }}
        </p>
      </div>

      <!-- 操作区域 -->
      <div v-if="showArrow || $slots.action" class="big-flex big-items-center big-flex-shrink-0">
        <slot name="action">
          <i
            v-if="showArrow"
            class="iconfont-sys big-text-lg big-text-gray-400 big-transition-all big-duration-300 hover:big-text-blue-500 hover:big-translate-x-1"
          >
            &#xe703;
          </i>
        </slot>
      </div>
    </div>

    <!-- 底部进度条 -->
    <div v-if="progress !== undefined" class="big-absolute big-bottom-0 big-left-0 big-right-0 big-h-1 big-bg-gray-200 big-rounded-b-xl big-overflow-hidden">
      <div
        class="big-h-full big-transition-all big-duration-1000 big-ease-out big-relative big-overflow-hidden"
        :style="{ width: progress + '%', backgroundColor: progressColor }"
      >
        <!-- 进度条光泽效果 -->
        <div class="big-absolute big-top-0 big-left-0 big-right-0 big-bottom-0 big-bg-gradient-to-r big-from-transparent big-via-white big-to-transparent big-opacity-40 big-animate-pulse"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import ArtCountTo from '@/components/core/text-effect/art-count-to/index.vue'

  defineOptions({ name: 'ArtStatsCard' })

  interface StatsCardProps {
    /** 图标 */
    icon?: string
    /** 标题 */
    title?: string
    /** 数值 */
    count?: number
    /** 描述 */
    description?: string
    /** 卡片变体 */
    variant?: 'default' | 'primary' | 'success' | 'warning' | 'error' | 'gradient'
    /** 图标样式 */
    iconStyle?: 'circle' | 'square' | 'rounded' | 'minimal'
    /** 图标颜色 */
    iconColor?: string
    /** 图标背景颜色 */
    iconBgColor?: string
    /** 图标大小 */
    iconSize?: number
    /** 背景颜色 */
    backgroundColor?: string
    /** 是否显示箭头 */
    showArrow?: boolean
    /** 是否可点击 */
    clickable?: boolean
    /** 是否显示装饰元素 */
    showDecoration?: boolean
    /** 动画持续时间 */
    animationDuration?: number
    /** 数字分隔符 */
    separator?: string
    /** 前缀 */
    prefix?: string
    /** 后缀 */
    suffix?: string
    /** 小数位数 */
    decimals?: number
    /** 单位 */
    unit?: string
    /** 趋势百分比 */
    trend?: number
    /** 进度百分比 */
    progress?: number
    /** 进度条颜色 */
    progressColor?: string
  }

  interface StatsCardEmits {
    click: [event: MouseEvent]
  }

  const props = withDefaults(defineProps<StatsCardProps>(), {
    variant: 'default',
    iconStyle: 'circle',
    iconSize: 32,
    animationDuration: 2000,
    separator: ',',
    decimals: 0,
    showDecoration: true
  })

  const emit = defineEmits<StatsCardEmits>()

  // 获取主题色
  const getThemeColor = (colorName: string) => {
    return getComputedStyle(document.documentElement)
      .getPropertyValue(`--el-color-${colorName}`)
      .trim() || '#409EFF'
  }

  // 变体样式类
  const variantClasses = computed(() => {
    const baseClasses = 'big-bg-white big-border big-border-gray-200 big-rounded-xl big-shadow-sm'

    switch (props.variant) {
      case 'primary':
        return `${baseClasses} big-bg-gradient-to-br big-from-blue-50 big-to-blue-100 big-border-blue-200`
      case 'success':
        return `${baseClasses} big-bg-gradient-to-br big-from-green-50 big-to-green-100 big-border-green-200`
      case 'warning':
        return `${baseClasses} big-bg-gradient-to-br big-from-yellow-50 big-to-yellow-100 big-border-yellow-200`
      case 'error':
        return `${baseClasses} big-bg-gradient-to-br big-from-red-50 big-to-red-100 big-border-red-200`
      case 'gradient':
        return `${baseClasses} big-from-blue-50 big-via-green-50 big-to-yellow-50 big-border-0`
      default:
        return baseClasses
    }
  })

  // 图标样式类
  const iconStyleClasses = computed(() => {
    const baseClasses = 'big-bg-blue-500'

    switch (props.iconStyle) {
      case 'circle':
        return `${baseClasses} big-rounded-full`
      case 'square':
        return `${baseClasses} big-rounded-md`
      case 'rounded':
        return `${baseClasses} big-rounded-2xl`
      case 'minimal':
        return 'big-bg-blue-100 big-text-blue-600 big-border-2 big-border-blue-200 big-rounded-xl'
      default:
        return `${baseClasses} big-rounded-full`
    }
  })

  // 卡片样式
  const cardStyle = computed(() => {
    const styles: Record<string, string> = {}

    if (props.backgroundColor) {
      styles.backgroundColor = props.backgroundColor
    }

    return styles
  })

  // 图标容器样式
  const iconContainerStyle = computed(() => {
    const styles: Record<string, string> = {}

    if (props.iconBgColor) {
      styles.backgroundColor = props.iconBgColor
    }

    return styles
  })

  // 趋势相关计算
  const trendClass = computed(() => {
    if (props.trend === undefined) return ''
    return props.trend >= 0 ? 'big-text-green-600' : 'big-text-red-600'
  })

  const trendIcon = computed(() => {
    if (props.trend === undefined) return ''
    return props.trend >= 0 ? '&#xe7e8;' : '&#xe7e9;' // 上升/下降箭头图标
  })

  // 进度条颜色
  const progressColor = computed(() => {
    if (props.progressColor) return props.progressColor

    switch (props.variant) {
      case 'primary': return getThemeColor('primary')
      case 'success': return getThemeColor('success')
      case 'warning': return getThemeColor('warning')
      case 'error': return getThemeColor('error')
      default: return getThemeColor('primary')
    }
  })

  // 点击处理
  const handleClick = (event: MouseEvent) => {
    if (props.clickable) {
      emit('click', event)
    }
  }
</script>

<style lang="scss" scoped>
  // 使用 Tailwind CSS，保留少量必要的自定义样式
  .art-custom-card {
    // 确保与设计系统兼容
    background-color: var(--art-main-bg-color);
    border-color: var(--art-border-color);
    box-shadow: var(--art-card-shadow);

    // 暗色主题适配
    @media (prefers-color-scheme: dark) {
      .big-bg-white {
        background-color: var(--art-main-bg-color) !important;
      }

      .big-text-gray-900 {
        color: var(--art-text-gray-100) !important;
      }

      .big-text-gray-600 {
        color: var(--art-text-gray-400) !important;
      }

      .big-text-gray-500 {
        color: var(--art-text-gray-500) !important;
      }

      .big-border-gray-200 {
        border-color: var(--art-border-color) !important;
      }
    }
  }

  // 自定义动画
  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  // 响应式优化
  @media (max-width: 768px) {
    .art-custom-card {
      .big-p-6 {
        padding: 1.25rem !important;
      }

      .big-text-2xl {
        font-size: 1.75rem !important;
      }

      .big-w-14 {
        width: 3rem !important;
      }

      .big-h-14 {
        height: 3rem !important;
      }
    }
  }
</style>
